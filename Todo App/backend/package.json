{"name": "todo-backend", "version": "1.0.0", "description": "Node.js backend for Todo application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "echo 'No linting configured for backend'", "lint:fix": "echo 'No linting configured for backend'"}, "keywords": ["todo", "api", "nodejs", "express"], "author": "Test Automation Framework", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "express-slow-down": "^2.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0"}}