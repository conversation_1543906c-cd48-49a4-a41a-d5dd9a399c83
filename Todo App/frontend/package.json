{"name": "todo-frontend", "version": "1.0.0", "description": "React Todo application frontend", "private": true, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.6.2", "react-router-dom": "^6.20.1", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "react-query": "^3.39.3", "react-hook-form": "^7.48.2", "react-intersection-observer": "^9.5.3", "framer-motion": "^10.16.16"}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/node": "^20.10.5", "typescript": "^4.9.5", "eslint": "^8.56.0", "eslint-config-react-app": "^7.0.1", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint src --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write src/**/*.{js,jsx,ts,tsx,css,md}"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:3001"}