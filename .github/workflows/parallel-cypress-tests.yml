name: Parallel Cypress E2E Tests

on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

env:
  NODE_VERSION: "18"

jobs:
  cypress-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 25
    strategy:
      fail-fast: false
      matrix:
        test-suite: [smoke, regression]
        include:
          - test-suite: smoke
            tag: '@Smoke'
            description: 'Smoke Tests'
            backend-port: 3001
            frontend-port: 3000
          - test-suite: regression
            tag: '@Regression'
            description: 'Regression Tests'
            backend-port: 3002
            frontend-port: 3003

    env:
      CYPRESS_BASE_URL: http://localhost:${{ matrix.frontend-port }}
      CYPRESS_TODO_API_URL: http://localhost:${{ matrix.backend-port }}
      CYPRESS_TODO_APP_URL: http://localhost:${{ matrix.frontend-port }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          # Temporarily disable cache to avoid npm issues
          # cache: 'npm'

      # Clean up any existing node_modules and cache
      - name: Clean up existing installations
        run: |
          rm -rf node_modules
          rm -rf "Todo App/frontend/node_modules"
          rm -rf "Todo App/backend/node_modules"
          rm -rf "Automation Framework/node_modules"
          npm cache clean --force
          # Remove problematic cache directories
          rm -rf /home/<USER>/.npm/_cacache
          rm -rf /home/<USER>/.npm/_logs

      # Regenerate package-lock.json files completely
      - name: Regenerate package-lock.json files
        run: |
          rm -f package-lock.json
          rm -f "Todo App/frontend/package-lock.json"
          rm -f "Todo App/backend/package-lock.json"
          npm install --package-lock-only
          cd "Todo App/frontend" && npm install --package-lock-only
          cd "../backend" && npm install --package-lock-only
        env:
          NODE_ENV: development

      # Install dependencies with better error handling
      - name: Install dependencies
        run: |
          npm install --no-audit --no-fund --prefer-offline --legacy-peer-deps
          cd "Todo App/frontend" && npm install --no-audit --no-fund --prefer-offline --legacy-peer-deps
          cd "../backend" && npm install --no-audit --no-fund --prefer-offline --legacy-peer-deps
        env:
          NODE_ENV: development

      - name: Install Cypress
        run: npm run cy:verify
        env:
          NODE_ENV: development

      # Start backend server with health check
      - name: Start backend server
        run: |
          cd "Todo App/backend"
          PORT=${{ matrix.backend-port }} npm start &
          echo "Backend server starting on port ${{ matrix.backend-port }}..."
          # Wait for backend to be ready
          timeout 60 bash -c 'until curl -f http://localhost:${{ matrix.backend-port }}/health; do sleep 2; done'
          echo "Backend server is ready!"
        env:
          NODE_ENV: development
          CI: true

      # Start frontend server with health check
      - name: Start frontend server
        run: |
          cd "Todo App/frontend"
          PORT=${{ matrix.frontend-port }} REACT_APP_API_URL=http://localhost:${{ matrix.backend-port }} BROWSER=none npm start &
          echo "Frontend server starting on port ${{ matrix.frontend-port }}..."
          # Wait for frontend to be ready
          timeout 60 bash -c 'until curl -f http://localhost:${{ matrix.frontend-port }}; do sleep 2; done'
          echo "Frontend server is ready!"
        env:
          REACT_APP_API_URL: http://localhost:${{ matrix.backend-port }}
          NODE_ENV: development
          CI: true

      # Verify servers are running
      - name: Verify servers are running
        run: |
          echo "Verifying servers..."
          curl -f http://localhost:${{ matrix.frontend-port }} || exit 1
          curl -f http://localhost:${{ matrix.backend-port }}/health || exit 1
          echo "All servers are running successfully!"

      # Run Cypress tests directly
      - name: Run ${{ matrix.description }}
        run: |
          cd "Automation Framework"
          npx cypress run --env grepTags=${{ matrix.tag }},grepFilterSpecs=true
        env:
          CI: true
          CYPRESS_BASE_URL: http://localhost:${{ matrix.frontend-port }}
          CYPRESS_TODO_API_URL: http://localhost:${{ matrix.backend-port }}
          CYPRESS_TODO_APP_URL: http://localhost:${{ matrix.frontend-port }}

      # Upload artifacts
      - name: Upload test report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.description }} Report
          path: Automation Framework/cypress/reports/html

      - name: Upload screenshots
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.description }} Screenshots
          path: Automation Framework/cypress/screenshots

      - name: Upload videos
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.description }} Videos
          path: Automation Framework/cypress/videos 